/**
 * Widget template management utilities
 */

import type { WidgetTemplate, WidgetGenerationOptions } from "../types.js";

export class WidgetTemplateManager {
  private templates: Map<string, WidgetTemplate>;

  constructor() {
    this.templates = new Map();
    this.initializeTemplates();
  }

  /**
   * Initialize predefined widget templates
   */
  private initializeTemplates(): void {
    // Button Widget Template
    this.templates.set("button", {
      name: "Button Widget",
      description: "Interactive button component with customizable actions",
      baseComponent: "Button",
      defaultProps: {
        title: "Click me",
        variant: "primary",
        action: "default",
      },
      imports: [
        'import { Button } from "antd";',
        'import React from "react";',
        'import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";',
      ],
    });

    // Form Widget Template
    this.templates.set("form", {
      name: "Form Widget",
      description: "Form input component with validation and submission",
      baseComponent: "Form",
      defaultProps: {
        title: "Form Widget",
        fields: [],
        submitText: "Submit",
      },
      imports: [
        'import { Form, Input, Button } from "antd";',
        'import React, { useState } from "react";',
        'import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";',
      ],
    });

    // Select Widget Template
    this.templates.set("select", {
      name: "Select Widget",
      description: "Dropdown selection component with static or API data",
      baseComponent: "Select",
      defaultProps: {
        placeholder: "Please select",
        options: [],
        defaultValue: "",
      },
      imports: [
        'import { Select } from "antd";',
        'import React, { useState, useEffect } from "react";',
        'import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";',
      ],
    });

    // Display Widget Template
    this.templates.set("display", {
      name: "Display Widget",
      description: "Data display component for showing information",
      baseComponent: "Card",
      defaultProps: {
        title: "Data Display",
        data: {},
      },
      imports: [
        'import { Card, Typography } from "antd";',
        'import React from "react";',
      ],
    });
  }

  /**
   * Get template by type
   */
  getTemplate(type: string): WidgetTemplate | null {
    return this.templates.get(type) || null;
  }

  /**
   * Get all available templates
   */
  getAllTemplates(): WidgetTemplate[] {
    return Array.from(this.templates.values());
  }

  /**
   * Generate component code from template
   */
  generateFromTemplate(template: WidgetTemplate, options: WidgetGenerationOptions): string {
    const componentName = options.name;
    const propsInterface = `${componentName}Props`;

    switch (template.baseComponent) {
      case "Button":
        return this.generateButtonWidget(componentName, propsInterface, options);
      case "Form":
        return this.generateFormWidget(componentName, propsInterface, options);
      case "Select":
        return this.generateSelectWidget(componentName, propsInterface, options);
      case "Card":
        return this.generateDisplayWidget(componentName, propsInterface, options);
      default:
        return this.generateGenericWidget(componentName, propsInterface, options, template);
    }
  }

  /**
   * Generate Button Widget
   */
  private generateButtonWidget(componentName: string, propsInterface: string, options: WidgetGenerationOptions): string {
    return `import { Button } from "antd";
import React from "react";

import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";

interface ${propsInterface} {
  title?: string;
  variant?: "primary" | "default" | "dashed" | "text" | "link";
  action?: "sendMessage" | "openSidePanel" | "insertText" | "custom";
  disabled?: boolean;
}

const ${componentName}: React.FC<${propsInterface}> = (props) => {
  const { title = "Click me", variant = "primary", action = "sendMessage", disabled = false } = props;
  const runner = useCommandRunner();

  const handleClick = () => {
    switch (action) {
      case "sendMessage":
        runner(BuildInCommand.SendMessage, {
          message: \`Action triggered by \${title}\`,
        });
        break;
      case "openSidePanel":
        runner(BuildInCommand.OpenSidePanel, { width: 400 });
        break;
      case "insertText":
        runner(BuildInCommand.InsertTextIntoSender, {
          text: \`Text from \${title}\`,
        });
        break;
      default:
        console.log("Custom action triggered");
    }
  };

  return (
    <Button type={variant} onClick={handleClick} disabled={disabled}>
      {title}
    </Button>
  );
};

export default ${componentName};
`;
  }

  /**
   * Generate Form Widget
   */
  private generateFormWidget(componentName: string, propsInterface: string, options: WidgetGenerationOptions): string {
    return `import { Form, Input, Button, message } from "antd";
import React from "react";

import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";

interface FormField {
  name: string;
  label: string;
  type: "text" | "textarea" | "number" | "email";
  required?: boolean;
  placeholder?: string;
}

interface ${propsInterface} {
  title?: string;
  fields?: FormField[];
  submitText?: string;
  onSubmit?: (values: any) => void;
}

const ${componentName}: React.FC<${propsInterface}> = (props) => {
  const { title = "Form Widget", fields = [], submitText = "Submit", onSubmit } = props;
  const [form] = Form.useForm();
  const runner = useCommandRunner();

  const handleSubmit = (values: any) => {
    if (onSubmit) {
      onSubmit(values);
    } else {
      // Default action: send form data as message
      const formData = JSON.stringify(values, null, 2);
      runner(BuildInCommand.SendMessage, {
        message: \`Form submitted:\\n\${formData}\`,
      });
    }
    
    message.success("Form submitted successfully!");
    form.resetFields();
  };

  const renderField = (field: FormField) => {
    const rules = field.required ? [{ required: true, message: \`Please input \${field.label}!\` }] : [];

    switch (field.type) {
      case "textarea":
        return (
          <Form.Item key={field.name} name={field.name} label={field.label} rules={rules}>
            <Input.TextArea placeholder={field.placeholder} rows={4} />
          </Form.Item>
        );
      case "number":
        return (
          <Form.Item key={field.name} name={field.name} label={field.label} rules={rules}>
            <Input type="number" placeholder={field.placeholder} />
          </Form.Item>
        );
      case "email":
        return (
          <Form.Item key={field.name} name={field.name} label={field.label} rules={[
            ...rules,
            { type: "email", message: "Please enter a valid email!" }
          ]}>
            <Input type="email" placeholder={field.placeholder} />
          </Form.Item>
        );
      default:
        return (
          <Form.Item key={field.name} name={field.name} label={field.label} rules={rules}>
            <Input placeholder={field.placeholder} />
          </Form.Item>
        );
    }
  };

  return (
    <div style={{ padding: "16px", border: "1px solid #d9d9d9", borderRadius: "6px" }}>
      <h3>{title}</h3>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        {fields.map(renderField)}
        <Form.Item>
          <Button type="primary" htmlType="submit">
            {submitText}
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ${componentName};
`;
  }

  /**
   * Generate Select Widget
   */
  private generateSelectWidget(componentName: string, propsInterface: string, options: WidgetGenerationOptions): string {
    return `import { Select, Spin } from "antd";
import React, { useState, useEffect } from "react";

import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";

interface SelectOption {
  label: string;
  value: string;
}

interface ${propsInterface} {
  placeholder?: string;
  options?: SelectOption[];
  defaultValue?: string;
  apiUrl?: string;
  onSelect?: (value: string) => void;
  disabled?: boolean;
}

const ${componentName}: React.FC<${propsInterface}> = (props) => {
  const { 
    placeholder = "Please select", 
    options = [], 
    defaultValue, 
    apiUrl, 
    onSelect, 
    disabled = false 
  } = props;
  
  const [loading, setLoading] = useState(false);
  const [dynamicOptions, setDynamicOptions] = useState<SelectOption[]>([]);
  const [value, setValue] = useState<string | undefined>(defaultValue);
  const runner = useCommandRunner();

  // Fetch options from API if provided
  useEffect(() => {
    if (apiUrl) {
      setLoading(true);
      fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
          // Assume API returns array of {label, value} objects
          setDynamicOptions(Array.isArray(data) ? data : []);
        })
        .catch(error => {
          console.error("Failed to fetch options:", error);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [apiUrl]);

  const allOptions = [...options, ...dynamicOptions];

  const handleChange = (selectedValue: string) => {
    setValue(selectedValue);
    
    if (onSelect) {
      onSelect(selectedValue);
    } else {
      // Default action: send selection as message
      const selectedOption = allOptions.find(opt => opt.value === selectedValue);
      runner(BuildInCommand.SendMessage, {
        message: \`Selected: \${selectedOption?.label || selectedValue}\`,
      });
    }
  };

  return (
    <Select
      placeholder={loading ? "Loading..." : placeholder}
      value={value}
      onChange={handleChange}
      disabled={disabled || loading}
      loading={loading}
      style={{ width: "100%", minWidth: 200 }}
      notFoundContent={loading ? <Spin size="small" /> : "No data"}
    >
      {allOptions.map(option => (
        <Select.Option key={option.value} value={option.value}>
          {option.label}
        </Select.Option>
      ))}
    </Select>
  );
};

export default ${componentName};
`;
  }

  /**
   * Generate Display Widget
   */
  private generateDisplayWidget(componentName: string, propsInterface: string, options: WidgetGenerationOptions): string {
    return `import { Card, Typography, Descriptions } from "antd";
import React from "react";

const { Title, Text } = Typography;

interface ${propsInterface} {
  title?: string;
  data?: Record<string, any>;
  layout?: "horizontal" | "vertical";
  bordered?: boolean;
}

const ${componentName}: React.FC<${propsInterface}> = (props) => {
  const { title = "Data Display", data = {}, layout = "horizontal", bordered = true } = props;

  const renderData = () => {
    const entries = Object.entries(data);
    
    if (entries.length === 0) {
      return <Text type="secondary">No data to display</Text>;
    }

    return (
      <Descriptions layout={layout} bordered={bordered} size="small">
        {entries.map(([key, value]) => (
          <Descriptions.Item key={key} label={key}>
            {typeof value === "object" ? JSON.stringify(value) : String(value)}
          </Descriptions.Item>
        ))}
      </Descriptions>
    );
  };

  return (
    <Card size="small" style={{ margin: "8px 0" }}>
      <Title level={5}>{title}</Title>
      {renderData()}
    </Card>
  );
};

export default ${componentName};
`;
  }

  /**
   * Generate Generic Widget
   */
  private generateGenericWidget(
    componentName: string, 
    propsInterface: string, 
    options: WidgetGenerationOptions, 
    template: WidgetTemplate
  ): string {
    const imports = template.imports.join("\n");
    
    return `${imports}

interface ${propsInterface} {
  // Add your prop definitions here
}

const ${componentName}: React.FC<${propsInterface}> = (props) => {
  // Add your component logic here
  
  return (
    <div>
      <h3>${componentName}</h3>
      <p>${options.description || `A ${options.type} widget component.`}</p>
    </div>
  );
};

export default ${componentName};
`;
  }
}
