/**
 * Widget generation utilities
 */

import { existsSync, mkdirSync, writeFileSync } from "fs";
import { join } from "path";

import ora from "ora";

import type { WidgetGenerationOptions, WidgetTemplate } from "../types.js";
import { ConfigUpdater } from "./config-updater.js";
import { FileSystemManager } from "./filesystem-manager.js";
import { LLMGenerator } from "./llm-generator.js";
import { Logger } from "./logger.js";
import { ProjectAnalyzer } from "./project-analyzer.js";
import { WidgetTemplateManager } from "./widget-template-manager.js";

export class WidgetGenerator {
  private projectAnalyzer: ProjectAnalyzer;
  private templateManager: WidgetTemplateManager;
  private llmGenerator: LLMGenerator;
  private fsManager: FileSystemManager;
  private configUpdater: ConfigUpdater;

  constructor() {
    this.projectAnalyzer = new ProjectAnalyzer();
    this.templateManager = new WidgetTemplateManager();
    this.llmGenerator = new LLMGenerator();
    this.fsManager = new FileSystemManager();
    this.configUpdater = new ConfigUpdater();
  }

  /**
   * Generate widget component
   */
  async generateWidget(options: WidgetGenerationOptions): Promise<boolean> {
    Logger.info(`Generating ${options.type} widget '${options.name}'...`);

    // Validate project
    const projectValidation = this.projectAnalyzer.validateProject(options.targetPath);
    if (!projectValidation.valid) {
      Logger.error(projectValidation.error || "Project validation failed");
      return false;
    }

    // Check if widget already exists
    if (this.projectAnalyzer.widgetExists(options.targetPath, options.name)) {
      Logger.error(`Widget '${options.name}' already exists in the project`);
      return false;
    }

    const spinner = ora("Generating widget files...").start();

    try {
      // Analyze project structure
      const structure = this.projectAnalyzer.analyzeProject(options.targetPath);

      // Create widgets directory if it doesn't exist
      if (!existsSync(structure.widgetsDir)) {
        await this.fsManager.createDirectory(structure.widgetsDir);
      }

      // Create widget directory
      const widgetDir = join(structure.widgetsDir, options.name.toLowerCase());
      await this.fsManager.createDirectory(widgetDir);

      // Generate widget component
      let componentCode: string;
      if (options.type === "custom") {
        // Use LLM for custom widget generation
        componentCode = await this.llmGenerator.generateCustomWidget(options);
      } else {
        // Use template for standard widget types
        const template = this.templateManager.getTemplate(options.type);
        if (!template) {
          throw new Error(`Template not found for widget type: ${options.type}`);
        }
        componentCode = this.templateManager.generateFromTemplate(template, options);
      }

      // Write component file
      const componentPath = join(widgetDir, "index.tsx");
      await this.fsManager.writeFile(componentPath, componentCode);

      // Generate README file
      const readmeContent = this.generateReadme(options);
      const readmePath = join(widgetDir, "README.md");
      await this.fsManager.writeFile(readmePath, readmeContent);

      // Update agent config
      const widgetImportPath = `./widgets/${options.name.toLowerCase()}`;
      const widgetCode = `@Custom/${options.name}`;
      await this.configUpdater.updateAgentConfig(structure.agentConfigPath, options, widgetImportPath, widgetCode);

      spinner.succeed("Widget generated successfully!");

      // Show success message
      this.showSuccessMessage(options);

      return true;
    } catch (error) {
      spinner.fail("Failed to generate widget");
      Logger.error(`Generation failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      return false;
    }
  }







  /**
   * Generate README content for the widget
   */
  private generateReadme(options: WidgetGenerationOptions): string {
    return `# ${options.name} Widget

${options.description || `A ${options.type} widget component for CSCS Agent.`}

## Usage

This widget is automatically registered in your agent configuration and can be used in the ${options.placement} area.

## Props

\`\`\`typescript
interface ${options.name}Props {
  // Add your prop definitions here
}
\`\`\`

## Example

\`\`\`tsx
<${options.name} />
\`\`\`

## Generated

This widget was generated using the CSCS Agent CLI.
`;
  }

  /**
   * Show success message with next steps
   */
  private showSuccessMessage(options: WidgetGenerationOptions): void {
    Logger.newLine();
    Logger.success(`Widget '${options.name}' generated successfully!`);
    Logger.newLine();
    
    Logger.subtitle("Files created:");
    Logger.list([
      `src/widgets/${options.name.toLowerCase()}/index.tsx - Widget component`,
      `src/widgets/${options.name.toLowerCase()}/README.md - Documentation`,
      "agent-config.tsx - Updated with new widget registration",
    ]);

    Logger.newLine();
    Logger.subtitle("Next steps:");
    Logger.list([
      "Review the generated widget component",
      "Customize the widget props and functionality",
      "Test the widget in your application",
      "Update the README with proper documentation",
    ]);

    Logger.newLine();
  }
}
