import type { AgentChatConfig } from "@cscs-agent/core";
import TestButton from "./widgets/testbutton";

export const config: AgentChatConfig = {
  agents: [
    {
      name: "Generated Agent",
      code: "generated-agent",
      description: "Agent with generated widgets",
      message: {
        blocks: {
          widgets: [
            {
          code: "@Custom/TestButton",
          component: TestButton,
        }
          ],
        },
      },
      request: {
        chat: {
          url: "/api/chat",
          method: "POST",
        },
      },
    },
  ],
};
