/**
 * File system operations using @modelcontextprotocol/server-filesystem
 */

import { existsSync, mkdirSync, writeFileSync, readFileSync } from "fs";
import { dirname } from "path";

import { Logger } from "./logger.js";

export class FileSystemManager {
  /**
   * Create directory recursively
   */
  async createDirectory(dirPath: string): Promise<void> {
    try {
      if (!existsSync(dirPath)) {
        mkdirSync(dirPath, { recursive: true });
        Logger.info(`Created directory: ${dirPath}`);
      }
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Write file with content
   */
  async writeFile(filePath: string, content: string): Promise<void> {
    try {
      // Ensure directory exists
      const dir = dirname(filePath);
      await this.createDirectory(dir);

      writeFileSync(filePath, content, "utf8");
      Logger.info(`Created file: ${filePath}`);
    } catch (error) {
      throw new Error(`Failed to write file ${filePath}: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Read file content
   */
  async readFile(filePath: string): Promise<string> {
    try {
      return readFileSync(filePath, "utf8");
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Check if file exists
   */
  fileExists(filePath: string): boolean {
    return existsSync(filePath);
  }

  /**
   * Backup file before modification
   */
  async backupFile(filePath: string): Promise<string> {
    if (!this.fileExists(filePath)) {
      throw new Error(`File does not exist: ${filePath}`);
    }

    const backupPath = `${filePath}.backup.${Date.now()}`;
    const content = await this.readFile(filePath);
    await this.writeFile(backupPath, content);
    
    Logger.info(`Created backup: ${backupPath}`);
    return backupPath;
  }

  /**
   * Update file content with backup
   */
  async updateFile(filePath: string, content: string, createBackup: boolean = true): Promise<void> {
    try {
      if (createBackup && this.fileExists(filePath)) {
        await this.backupFile(filePath);
      }

      await this.writeFile(filePath, content);
    } catch (error) {
      throw new Error(`Failed to update file ${filePath}: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
