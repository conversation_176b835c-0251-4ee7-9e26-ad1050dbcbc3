import { Button } from "antd";
import React from "react";

import { useCommandRunner, BuildInCommand } from "@cscs-agent/core";

interface TestButtonProps {
  title?: string;
  variant?: "primary" | "default" | "dashed" | "text" | "link";
  action?: "sendMessage" | "openSidePanel" | "insertText" | "custom";
  disabled?: boolean;
}

const TestButton: React.FC<TestButtonProps> = (props) => {
  const { title = "Click me", variant = "primary", action = "sendMessage", disabled = false } = props;
  const runner = useCommandRunner();

  const handleClick = () => {
    switch (action) {
      case "sendMessage":
        runner(BuildInCommand.SendMessage, {
          message: `Action triggered by ${title}`,
        });
        break;
      case "openSidePanel":
        runner(BuildInCommand.OpenSidePanel, { width: 400 });
        break;
      case "insertText":
        runner(BuildInCommand.InsertTextIntoSender, {
          text: `Text from ${title}`,
        });
        break;
      default:
        console.log("Custom action triggered");
    }
  };

  return (
    <Button type={variant} onClick={handleClick} disabled={disabled}>
      {title}
    </Button>
  );
};

export default TestButton;
