#!/usr/bin/env node

/**
 * CSCS Agent CLI - Main entry point
 */

import { readFileSync } from "fs";
import { dirname, join } from "path";
import { fileURLToPath } from "url";

import { Command } from "commander";
import { cristal } from "gradient-string";

import { CreateCommand } from "./commands/create.js";
import { GenerateCommand } from "./commands/generate.js";
import { logo } from "./logo.js";
import { Logger } from "./utils/logger.js";

// Get package.json for version info
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const packageJsonPath = join(__dirname, "..", "package.json");
const packageJson = JSON.parse(readFileSync(packageJsonPath, "utf8"));

const program = new Command();

console.log(cristal(logo));

// Configure main program
program
  .name("cscs-agent-cli")
  .description("CLI tool for creating CSCS Agent projects from templates")
  .version(packageJson.version, "-v, --version", "display version number");

// Create command
program
  .command("create [project-name]")
  .description("Create a new CSCS Agent project")
  .option("-t, --template <template>", "Template to use")
  .option("-d, --directory <directory>", "Target directory (default: project name)")
  .option("--skip-install", "Skip dependency installation")
  .option("--package-manager <manager>", "Package manager to use (npm, yarn, pnpm)")
  .option("-i, --interactive", "Run in interactive mode")
  .action(async (projectName, options) => {
    const createCommand = new CreateCommand();
    await createCommand.execute(projectName, options);
  });

// Generate command
program
  .command("generate")
  .alias("g")
  .description("Generate widget components for CSCS Agent projects")
  .option("-t, --type <type>", "Widget type (button, form, select, display, custom)")
  .option("-n, --name <name>", "Widget name (PascalCase)")
  .option("-p, --target-path <path>", "Target project path", process.cwd())
  .option("-d, --description <description>", "Widget description")
  .option("--placement <placement>", "Widget placement (message, sender, sidePanel)")
  .option("--slot <slot>", "Widget slot (blocks, header, footer, headerPanel)")
  .option("--props <props>", "Widget props as JSON string")
  .option("-i, --interactive", "Run in interactive mode")
  .action(async (options) => {
    const generateCommand = new GenerateCommand();
    await generateCommand.execute(options);
  });

// List templates command
program
  .command("list")
  .alias("ls")
  .description("List available templates")
  .action(() => {
    const createCommand = new CreateCommand();
    createCommand.listTemplates();
  });

// List widget types command
program
  .command("list-widgets")
  .alias("lw")
  .description("List available widget types for generation")
  .action(() => {
    const generateCommand = new GenerateCommand();
    generateCommand.listWidgetTypes();
  });

// Templates command (alias for list)
program
  .command("templates")
  .description("List available templates")
  .action(() => {
    const createCommand = new CreateCommand();
    createCommand.listTemplates();
  });

// Info command
program
  .command("info")
  .description("Show CLI information")
  .action(() => {
    Logger.title("CSCS Agent CLI Information");

    Logger.table([
      { key: "Version", value: packageJson.version },
      { key: "Description", value: packageJson.description },
      { key: "Node Version", value: process.version },
      { key: "Platform", value: process.platform },
      { key: "Architecture", value: process.arch },
    ]);

    Logger.newLine();
    Logger.subtitle("Available Commands:");
    Logger.list([
      "create [project-name] - Create a new project",
      "generate - Generate widget components",
      "list - List available templates",
      "list-widgets - List available widget types",
      "info - Show CLI information",
      "help - Show help information",
    ]);

    Logger.newLine();
  });

// Global error handler
process.on("uncaughtException", (error) => {
  Logger.error(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

process.on("unhandledRejection", (reason) => {
  Logger.error(`Unhandled rejection: ${reason}`);
  process.exit(1);
});

// Custom help
program.on("--help", () => {
  console.log("");
  console.log("Examples:");
  console.log("  $ cscs-agent-cli create my-app");
  console.log("  $ cscs-agent-cli create my-app --template basic");
  console.log("  $ cscs-agent-cli create --interactive");
  console.log("  $ cscs-agent-cli list");
  console.log("");
  console.log("For more information, visit: https://github.com/your-org/cscs-agent");
});

// Parse arguments and execute
program.parse();

// If no command provided, show help
if (!process.argv.slice(2).length) {
  program.outputHelp();
}
