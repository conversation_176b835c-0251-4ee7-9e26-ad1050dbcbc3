# @cscs-agent/cli

A powerful CLI tool for creating CSCS Agent projects from templates with enhanced features including interactive template selection, cross-platform compatibility, and comprehensive error handling.

## Features

- 🚀 **Template-based project creation** - Create projects from predefined templates
- 🎯 **Interactive mode** - User-friendly prompts for template and configuration selection
- 🔍 **Template discovery** - Automatic discovery and validation of available templates
- 📦 **Smart package manager detection** - Automatically detects and uses pnpm, yarn, or npm
- ✅ **Project validation** - Validates project names and prevents conflicts
- 🎨 **Rich CLI experience** - Colored output, progress indicators, and clear feedback
- 🌍 **Cross-platform support** - Works on Windows, macOS, and Linux
- 🛠️ **Comprehensive error handling** - Detailed error messages and recovery suggestions

## Installation

### Global Installation (Recommended)

```bash
npm install -g @cscs-agent/cli
```

### Using npx (No Installation Required)

```bash
npx @cscs-agent/cli create my-project
```

## Usage

### Quick Start

Create a new project with the default template:

```bash
cscs-agent-cli create my-project
```

### Interactive Mode

Launch interactive mode for guided project creation:

```bash
cscs-agent-cli create --interactive
```

### Specify Template

Create a project with a specific template:

```bash
cscs-agent-cli create my-project --template basic
```

### Advanced Options

```bash
cscs-agent-cli create my-project \
  --template basic \
  --directory ./projects/my-project \
  --package-manager pnpm \
  --skip-install
```

## Commands

### `create [project-name]`

Create a new CSCS Agent project.

**Options:**
- `-t, --template <template>` - Template to use
- `-d, --directory <directory>` - Target directory (default: project name)
- `--skip-install` - Skip dependency installation
- `--package-manager <manager>` - Package manager to use (npm, yarn, pnpm)
- `-i, --interactive` - Run in interactive mode

**Examples:**
```bash
# Basic usage
cscs-agent-cli create my-app

# With specific template
cscs-agent-cli create my-app --template basic

# Interactive mode
cscs-agent-cli create --interactive

# Skip dependency installation
cscs-agent-cli create my-app --skip-install

# Use specific package manager
cscs-agent-cli create my-app --package-manager yarn
```

### `list` / `ls`

List all available templates with descriptions.

```bash
cscs-agent-cli list
```

### `templates`

Alias for the `list` command.

```bash
cscs-agent-cli templates
```

### `info`

Show CLI information including version, platform details, and available commands.

```bash
cscs-agent-cli info
```

## Templates

Templates are located in the `templates/` directory within the CLI package. Each template contains a complete project structure ready for development.

### Available Templates

- **basic** - A basic CSCS Agent application with essential features and configurations

### Template Structure

Each template should include:

```
template-name/
├── package.json          # Project metadata and dependencies
├── index.html           # Main HTML file
├── vite.config.js       # Vite configuration
├── tsconfig.json        # TypeScript configuration
├── src/
│   ├── main.tsx         # Application entry point
│   ├── agent-config.tsx # Agent configuration
│   └── pages/           # Application pages
└── public/              # Static assets
```

## Configuration

The CLI automatically detects and uses the best available package manager:

1. **pnpm** (preferred)
2. **yarn** (fallback)
3. **npm** (default fallback)

You can override this behavior using the `--package-manager` option.

## Error Handling

The CLI provides comprehensive error handling for common scenarios:

- **Invalid project names** - Validates npm package name format
- **Existing directories** - Prevents overwriting existing projects
- **Missing templates** - Lists available templates when specified template is not found
- **Network issues** - Graceful handling of dependency installation failures
- **Permission errors** - Clear guidance for permission-related issues

## Development

### Building from Source

```bash
# Clone the repository
git clone <repository-url>
cd agent/packages/cli

# Install dependencies
pnpm install

# Build the CLI
pnpm run build

# Test the CLI
pnpm run test
```

### Project Structure

```
packages/cli/
├── src/
│   ├── cli.ts                    # Main CLI entry point
│   ├── types.ts                  # Type definitions
│   ├── commands/
│   │   └── create.ts             # Create command implementation
│   └── utils/
│       ├── logger.ts             # Logging utilities
│       ├── template-manager.ts   # Template management
│       └── project-creator.ts    # Project creation logic
├── bin/
│   ├── cli.js                    # Compiled CLI entry point
│   └── create.js                 # Legacy compatibility wrapper
├── lib/                          # Compiled TypeScript output
├── templates/                    # Project templates
└── package.json
```

### Adding New Templates

1. Create a new directory in `templates/`
2. Add a complete project structure
3. Ensure `package.json` exists with proper metadata
4. Test the template with the CLI

Example template `package.json`:

```json
{
  "name": "my-template",
  "version": "1.0.0",
  "description": "My custom CSCS Agent template",
  "private": true,
  "type": "module",
  "scripts": {
    "dev": "vite --force",
    "build": "vite build",
    "lint": "eslint ./src --ext .ts,.tsx --fix",
    "preview": "vite preview"
  },
  "dependencies": {
    // ... your dependencies
  }
}
```

## Troubleshooting

### Common Issues

1. **"Templates directory not found"**
   - Ensure the CLI package is properly installed
   - Check that `templates/` directory exists in the CLI package

2. **"CLI module not found"**
   - Run `pnpm run build` in the CLI package directory
   - Ensure TypeScript compilation completed successfully

3. **"Permission denied"**
   - On Unix systems, ensure the CLI binary is executable
   - On Windows, run as administrator if needed

4. **"Project name validation failed"**
   - Use lowercase letters, numbers, hyphens, and underscores only
   - Ensure the name doesn't start with a number or special character

### Debug Information

Get debug information:

```bash
# Check CLI version and info
cscs-agent-cli info

# List available templates
cscs-agent-cli list

# Test with verbose output
DEBUG=* cscs-agent-cli create test-project
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the ISC License. See the main project LICENSE file for details.

## Support

For issues and questions:

- Check the [troubleshooting section](#troubleshooting)
- Review existing issues in the repository
- Create a new issue with detailed information about your problem

## Changelog

### v0.3.0

- Initial release with enhanced CLI functionality
- Interactive mode support
- Template discovery and validation
- Cross-platform compatibility
- Comprehensive error handling
- Rich CLI experience with colored output
